import type { NodeRuntimeContext, NodeRunResult, NodeChildRef } from '../../types'
import type { Settings } from './node.types'

export async function run({ ctx, settings, children }: { ctx: NodeRuntimeContext; settings: Settings; children?: NodeChildRef[] }): Promise<NodeRunResult> {
	console.log("Open screen: ", settings.screenId)
	ctx.setGameScene(ctx.graph.parentWidgetId, settings.screenId)
	return { next: children }
}



