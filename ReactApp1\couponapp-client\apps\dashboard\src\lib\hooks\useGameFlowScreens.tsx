import { useFlowGameScreens } from "@repo/shared/lib/flow/hooks/useFlowGameScreens"
import { useCampaignData } from "@repo/shared/lib/hooks/useCampaignStore"
import { useMemo } from "react"
import { useWidgetSettings } from "./useWidgetSettings"
import { useEditor } from "./useEditor"

export function useGameWidgetFlowScreens({gameWidgetId}: {gameWidgetId: string}) {
    const { findWidgetById, rootWidget} = useEditor()
    const gameWidget = useMemo(() => findWidgetById(rootWidget, gameWidgetId), [findWidgetById, rootWidget, gameWidgetId])
    const { settings } = useWidgetSettings(gameWidget)

    const { campaignData } = useCampaignData()
    const flows = campaignData?.campaign?.config?.flows || []
    const gameFlowId = settings?.gameConfig?.gameFlowId
    const flowGraph = useMemo(() => {
        return gameFlowId ? flows.find(f => f.id === gameFlowId) : null
    }, [flows, gameFlowId])

    // Get screens from flow graph
    const flowScreens = useFlowGameScreens(flowGraph)

    return flowScreens
}