import type { FlowGraph, GameScreenDefinition, NodeEditorContext } from '../types'
import type { Edge as RFEdge } from '@xyflow/react'
import { getNode } from '../registry'

/**
 * Get all edges connected to a specific node
 */
export function getConnectedEdges(graph: FlowGraph, nodeId: string): { inputEdges: RFEdge[], outputEdges: RFEdge[] } {
    const inputEdges = graph.edges.filter(edge => edge.target === nodeId)
    const outputEdges = graph.edges.filter(edge => edge.source === nodeId)
    
    return { inputEdges, outputEdges }
}

/**
 * Find all nodes in the graph that have exposeScreens method
 */
export function findNodesWithScreens(graph: FlowGraph): Array<{ nodeId: string, screens: GameScreenDefinition[] }> {
    const result: Array<{ nodeId: string, screens: GameScreenDefinition[] }> = []
    
    if(!graph) {
        return result
    }

    for (const node of graph.nodes) {
        const nodeData = node.data
        if (!nodeData?.nodeType) continue
        
        const nodeDef = getNode(nodeData.nodeType)
        if (!nodeDef?.exposeScreens) continue
        
        const { inputEdges, outputEdges } = getConnectedEdges(graph, node.id)
        
        const ctx: NodeEditorContext = {
            nodeId: node.id,
            graph,
            inputEdges,
            outputEdges
        }
        
        try {
            const screens = nodeDef.exposeScreens(nodeData.settings || {}, ctx)
            if (screens && screens.length > 0) {
                result.push({ nodeId: node.id, screens })
            }
        } catch (error) {
            console.warn(`Error getting screens from node ${node.id}:`, error)
        }
    }
    
    return result
}

/**
 * Get all game screens exposed by nodes in the flow graph
 */
export function getAllFlowGameScreens(graph: FlowGraph): GameScreenDefinition[] {
    const nodesWithScreens = findNodesWithScreens(graph)
    const allScreens: GameScreenDefinition[] = []
    
    for (const { screens } of nodesWithScreens) {
        allScreens.push(...screens)
    }
    
    return allScreens
}
